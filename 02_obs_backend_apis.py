import os
import json
from llm_helper import LLMAPIClient
from template_driven_writing import TemplateDrivenWriting
from config import Config<PERSON><PERSON><PERSON>

def tdw_api(input_template, input_resources_list, output_file, original_title, new_title):
    config = ConfigManager()
    # 确保输出目录存在
    os.makedirs(output_file, exist_ok=True)

    # 初始化LLM客户端
    llm_source = 'glm' # qwen, deepseek; 只修改这里就行
    api_key = config.get('apis.{llm_source}.api_key'.format(llm_source=llm_source))
    base_url = config.get('apis.{llm_source}.base_url'.format(llm_source=llm_source))
    model_name = config.get('apis.{llm_source}.model_name'.format(llm_source=llm_source))
    llm_client = LLMAPIClient(api_key,model_name=model_name,base_url=base_url)
    tdw_obj = TemplateDrivenWriting(temp_base_dir=output_file, llm_client=llm_client)
    tdw_obj.set_template(input_template)
    for input_resources in input_resources_list:
        tdw_obj.add_resource(input_resources)

    # 第一步：切割模版文档
    summary=''
    partitions = []
    retry_count = 0
    while len(partitions) == 0 and retry_count < 3: # 如果解析错误，重试一下
        retry_count = retry_count + 1
        print('第{i}次尝试解析文章结构...'.format(i=retry_count))
        summary, partitions = tdw_obj.partitions()
    if len(partitions) == 0:
        print('解析文章结构失败，请检查文章格式是否正确')
        return
    print('文章摘要：{summary}'.format(summary=summary))
    print('文章结构解析成功，共{num}个段落'.format(num=len(partitions)))
    for i, partition in enumerate(partitions):
        print('段落{num}：{summary}'.format(num=i+1, summary=partition['summary']))

    new_paragraphs = []
    # 第二~四步：每个段落串行处理
    for i, partition in enumerate(partitions):
        print('开始处理第{num}个段落...'.format(num=i+1))
        if original_title != new_title:
            # 第二步： 生成写作指导
            retry_count = 0
            instruction = ''
            warnings = ''
            while instruction == '' and retry_count < 3:
                retry_count = retry_count + 1
                # instruction, warnings = tdw_obj.object_replace(partition['summary'], original_title, new_title)
                instruction, warnings = tdw_obj.object_replace(partition['slice'], original_title, new_title)
            if instruction == '':
                print('生成写作指导失败，请检查文章格式是否正确')
                continue
            if isinstance(instruction, dict):
                instruction = json.dumps(instruction, ensure_ascii=False, indent=2)
            if isinstance(warnings, dict):
                warnings = json.dumps(warnings, ensure_ascii=False, indent=2)
            print('写作指导：{instruction}'.format(instruction=instruction))
            print('注意事项：{warnings}'.format(warnings=warnings))
            print('生成写作指导成功，开始搜索参考内容...')
            # 第三步： 搜索参考内容
            ref = []
            retry_count = 0
            while len(ref) == 0 and retry_count < 3:
                retry_count = retry_count + 1
                ref = tdw_obj.search(instruction)
            if len(ref) == 0:
                print('搜索参考内容失败，请检查文章格式是否正确')
                continue
            print('搜索到{j}个相关片段...'.format(j=len(ref)))
            # 将搜索到的段落列表转换为字符串，只包含段落内容
            ref_text = "'''"+ "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None])+"'''"
            # 第3.2步： 段落重写
            retry_count = 0
            rewrite_instruction = ''
            while rewrite_instruction == '' and retry_count < 3:
                retry_count = retry_count + 1
                rewrite_instruction = tdw_obj.rewrite_instruction(partition['summary'], original_title, new_title, ref_text, instruction, warnings)
            if rewrite_instruction == '':
                print('重写写作指导失败，请检查文章格式是否正确')
                continue
            if isinstance(rewrite_instruction, dict):
                rewrite_instruction = json.dumps(rewrite_instruction, ensure_ascii=False, indent=2)
            print('重写写作指导：{rewrite_instruction}'.format(rewrite_instruction=rewrite_instruction))
            print('重写写作指导成功，开始段落重写...')
            # 第四步： 段落重写
            retry_count = 0
            new_paragraph = ''
            while new_paragraph == '' and retry_count < 3:
                retry_count = retry_count + 1
                new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text, instruction )
            if new_paragraphs == '':
                print('段落重写失败，请检查文章格式是否正确')
                continue
            print('重写后段落：\n' + new_paragraph)
            print('段落重写成功，开始处理下一个段落...')
            new_paragraphs.append({'seq_no':i, 'paragraph':new_paragraph})
        else:
            # 第三步： 搜索参考内容
            ref = []
            retry_count = 0
            while len(ref) == 0 and retry_count < 3:
                retry_count = retry_count + 1
                ref = tdw_obj.search(partition['summary'])
            if len(ref) == 0:
                print('搜索参考内容失败，请检查文章格式是否正确')
                continue
            print('搜索到{j}个相关片段...'.format(j=len(ref)))
            # 将搜索到的段落列表转换为字符串，只包含段落内容
            ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"

            # 第四步： 段落重写
            retry_count = 0
            new_paragraph = ''
            while new_paragraph == '' and retry_count < 3:
                retry_count = retry_count + 1
                new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text, '根据素材对片段进行优化，补充缺失的内容，修复错误的信息')
            if new_paragraphs == '':
                print('段落重写失败，请检查文章格式是否正确')
                continue
            print('重写后段落：\n' + new_paragraph)
            print('段落重写成功，开始处理下一个段落...')
            new_paragraphs.append({'seq_no': i, 'paragraph': new_paragraph})

    # # 第二~四步：每个段落可以并行执行
    # import threading
    # from concurrent.futures import ThreadPoolExecutor, as_completed
    #
    # def process_partition(partition, idx):
    #     # 第二步： 生成写作指导
    #     retry_count = 0
    #     instruction = ''
    #     warnings = ''
    #     while instruction == '' and retry_count < 3:
    #         retry_count = retry_count + 1
    #         instruction, warnings = tdw_obj.object_replace(partition['slice'], original_title, new_title)
    #     if instruction == '':
    #         print('生成写作指导失败，请检查文章格式是否正确')
    #         return {'seq_no': idx, 'paragraph': None}
    #
    #     # 第三步： 搜索参考内容
    #     ref = []
    #     retry_count = 0
    #     while len(ref) == 0 and retry_count < 3:
    #         retry_count = retry_count + 1
    #         ref = tdw_obj.search(instruction)
    #     if len(ref) == 0:
    #         print('搜索参考内容失败，请检查文章格式是否正确')
    #         return {'seq_no': idx, 'paragraph': None}
    #
    #     # 第四步： 段落重写
    #     retry_count = 0
    #     new_paragraph = ''
    #     while new_paragraph == '' and retry_count < 3:
    #         retry_count = retry_count + 1
    #         new_paragraph = tdw_obj.rewrite(partition['slice'], ref, instruction + '\n\n' + warnings)
    #     if new_paragraph == '':
    #         print('段落重写失败，请检查文章格式是否正确')
    #         return {'seq_no': idx, 'paragraph': None}
    #
    #     return {'seq_no': idx, 'paragraph': new_paragraph}
    #
    # # 使用线程池并行处理
    # new_paragraphs = []
    # with ThreadPoolExecutor(max_workers=min(10, len(partitions))) as executor:
    #     # 提交所有任务
    #     future_to_idx = {executor.submit(process_partition, partition, i): i
    #                      for i, partition in enumerate(partitions)}
    #
    #     # 收集结果
    #     for future in as_completed(future_to_idx):
    #         result = future.result()
    #         new_paragraphs.append(result)
    #
    # # 检查是否有任何段落处理失败
    # if any(p['paragraph'] is None for p in new_paragraphs):
    #     print('一个或多个段落处理失败')
    #     return
    #
    # # 按序号排序结果
    # new_paragraphs.sort(key=lambda x: x['seq_no'])

    # 第五步：聚合成文章
    # 将段落列表转换为字符串，只包含段落内容
    paragraphs_text = "\n\n".join([f"段落 {item['seq_no'] + 1}:\n{item['paragraph']}\n\n" for item in
                                   sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                   if item['paragraph'] is not None])
    paragraphs_text_printout = "\n\n".join([f"{item['paragraph']}\n\n" for item in
                                   sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                   if item['paragraph'] is not None])

    print('聚合前段落文章：\n' + paragraphs_text_printout)
    # TODO：超长文章超过模型输出max token的问题：4K = 6000字，这种情况下润色做不了了。
    #  如果文章超过5000字，需要按照每4000字进行滚动切片，也就是0～4000字第一切片，2000～6000字第二切片，4000～8000字第三切片。
    #  最后用规则做聚合。0～3000字用第一切片，3000～5000字用第二切片，5000～7000字用第三切片以此类推。

    new_article = tdw_obj.para_merge(paragraphs_text)
    print('文章聚合成功')
    print('聚合文章：\n' + new_article)
    # 第六步：文章润色
    polished = tdw_obj.polish(new_article)
    print('文章润色成功')
    print('最终文章：\n' + polished)
    return str(paragraphs_text_printout), str(new_article), str(polished)


if __name__ == '__main__':
    # input_template = 'asset/副本关于XX机构2024年年（半年、X季度）报的热点评述（财报热点评述模板）.docx'
    # input_resources_list = ['asset/雅安市商业银行2024年度报告.pdf', 'asset/雅安市商业银行股份有限公司2024年度信息披露报告.pdf.pdf']
    # output_file = 'output'
    # original_title = '恒丰银行'
    # new_title = '雅安市商业银行'
    # polished_article = tdw_api(input_template, input_resources_list, output_file, original_title, new_title)

    # input_template = 'asset/天津市债务融资工具总体情况.docx'
    # input_resources_list = ['asset/北京市债务融资工具发展报告：历史演进、2024年现状与未来展望.docx']
    # output_file = 'output'
    # original_title = '天津市'
    # new_title = '北京市'
    # polished_article = tdw_api(input_template, input_resources_list, output_file, original_title, new_title)

    input_template = 'asset/短一-天淮钢管授信尽调报告20250325-清洁版(2)_副本.docx'
    input_resources_list = ['asset/天淮公司-章程.pdf','asset/页面提取自－2021年审计报告.pdf','asset/页面提取自－2022年审计报告-全.pdf','asset/页面提取自－2023年审计报告.pdf','asset/2024年12月财务月报-江苏天淮钢管有限公司(2).pdf']
    output_file = 'output'
    original_title = '天淮钢管授信尽调报告'
    new_title = '天淮钢管授信尽调报告'
    polished_article = tdw_api(input_template, input_resources_list, output_file, original_title, new_title)
