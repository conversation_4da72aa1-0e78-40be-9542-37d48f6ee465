
import cv2
import easyocr
from paddleocr import PaddleOCR
def parse_handwritting_document(handwritting_path,output_text_path):
    output_text = handwritting_parser(handwritting_path)
    with open(output_text_path, 'w', encoding='utf-8') as text_file:
        text_file.write(output_text)

def handwritting_parser(handwritting_path, conf_thr: float = 0.2):
    ocr = PaddleOCR(use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
        enable_mkldnn=False)
    result = ocr.predict(handwritting_path)
    output_str = ""
    for res in result:
        for k in res:
            print(k)
        output_str +=''.join(res['rec_texts'])
        # res.print()
        # res.save_to_img("test_output/1")
        # res.save_to_json("test_output/2")
    # for line in result:
    #     print(line)
        # output_str += line
        # if line[1][1] > conf_thr:
        #     output_str += line[1][0] + " "
    return output_str.strip()

# def parse_img_document(img_path, output_text_path):
#     output_text = img_ocr_parser(img_path)
#     with open(output_text_path, 'w', encoding='utf-8') as text_file:
#         text_file.write(output_text)
#
# def img_ocr_parser(img_path, conf_thr: float = 0.2):
#     img = cv2.imread(img_path)
#     reader = easyocr.Reader(["en", "ch_sim"],model_storage_directory='./asset/model')
#     result = reader.readtext(img)
#     output_str = ""
#     for _, text, conf in result:
#         if conf > conf_thr:
#             output_str += text + " "
#     return output_str.strip()

if __name__ == "__main__":
    # parse_img_document('asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/报案书/截图20250609154213.png', 'test_output/test报案书1.txt')
    res = parse_handwritting_document('asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/报案书/截图20250609154213.png', 'test_output/test报案书2.txt')
    # print(res)